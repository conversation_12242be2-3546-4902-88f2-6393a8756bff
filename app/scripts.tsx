"use client";

import { GTM_CONFIG, OSANO_CONFIG } from "@/configs/analytics";
import <PERSON><PERSON><PERSON> from "next/script";
import { useEffect } from "react";
import { initializeAccessibilityFixes } from "@/lib/accessibility-fixes";
import {
  generateHashFromUrl,
  generateScriptIntegrityHash,
} from "@/lib/script-integrity";

export function Scripts() {
  const oapName = `${
    process.env.NEXT_PUBLIC_OAP_NAME
  }_${process.env.NEXT_PUBLIC_NODE_ENV?.toUpperCase()}`;

  if (!oapName || !GTM_CONFIG[oapName]) {
    console.log("No oapName or GTM_CONFIG[oapName]", oapName);
  }
  if (!OSANO_CONFIG[oapName]) {
    console.log("No oapName or OSANO_CONFIG[oapName]", oapName);
  }

  const gtmConfig = GTM_CONFIG[oapName];
  const osanoId = OSANO_CONFIG[oapName];
  const isProd = process.env.NEXT_PUBLIC_NODE_ENV === "prod";

  // Initialize accessibility fixes for react-select components
  useEffect(() => {
    const cleanup = initializeAccessibilityFixes();
    return cleanup;
  }, []);

  return (
    <>
      {/* React-Select Accessibility Fixes */}
      <Script id="react-select-accessibility" strategy="afterInteractive">
        {`
          // Inline accessibility fixes for react-select components
          function addReactSelectRoles() {
            // Fix dropdown indicators
            document.querySelectorAll('.react-select__dropdown-indicator').forEach(el => {
              if (!el.getAttribute('role')) {
                el.setAttribute('role', 'button');
                el.setAttribute('aria-label', 'Open dropdown menu');
                // Only add tabindex if element doesn't already have one
                if (!el.getAttribute('tabindex')) {
                  el.setAttribute('tabindex', '0');
                }
              }
            });

            // Fix clear indicators
            document.querySelectorAll('.react-select__clear-indicator').forEach(el => {
              if (!el.getAttribute('role')) {
                el.setAttribute('role', 'button');
                el.setAttribute('aria-label', 'Clear selection');
                // Only add tabindex if element doesn't already have one
                if (!el.getAttribute('tabindex')) {
                  el.setAttribute('tabindex', '0');
                }
              }
            });

            // Fix multi-value remove buttons
            document.querySelectorAll('.react-select__multi-value__remove').forEach(el => {
              if (!el.getAttribute('role')) {
                el.setAttribute('role', 'button');
                const valueText = el.closest('.react-select__multi-value')?.querySelector('.react-select__multi-value__label')?.textContent || 'item';
                el.setAttribute('aria-label', 'Remove ' + valueText);
                // Only add tabindex if element doesn't already have one
                if (!el.getAttribute('tabindex')) {
                  el.setAttribute('tabindex', '0');
                }
              }
            });

            // Fix single values
            document.querySelectorAll('.react-select__single-value').forEach(el => {
              if (!el.getAttribute('role')) {
                el.setAttribute('role', 'button');
                el.setAttribute('aria-readonly', 'true');
                // Only add tabindex if element doesn't already have one
                if (!el.getAttribute('tabindex')) {
                  el.setAttribute('tabindex', '0');
                }
              }
            });

            // Fix controogs
            document.querySelectorAll('.react-select__control').forEach(el => {
              if (!el.getAttribute('role')) {
                el.setAttribute('role', 'combobox');
                el.setAttribute('aria-haspopup', 'listbox');
                const menuIsOpen = el.closest('.react-select__container')?.querySelector('.react-select__menu') !== null;
                el.setAttribute('aria-expanded', menuIsOpen.toString());
              }
            });

            // Fix specific problematic CSS classes from accessibility report
            document.querySelectorAll('.css-7pg0cj-a11yText, .css-9np160-singleValue, .css-1wy0on6, .css-1hyfx7x, .css-187t9pu-indicatorContainer').forEach(el => {
              if (el.classList.contains('css-9np160-singleValue') || el.classList.contains('css-7pg0cj-a11yText')) {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'button');
                  el.setAttribute('aria-readonly', 'true');
                  el.setAttribute('aria-label', 'Open dropdown menu');
                  // Only add tabindex if element doesn't already have one
                  if (!el.getAttribute('tabindex')) {
                    el.setAttribute('tabindex', '0');
                  }
                }
              } else if (el.classList.contains('css-187t9pu-indicatorContainer')) {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'button');
                  el.setAttribute('aria-label', 'Dropdown indicator');
                  // Only add tabindex if element doesn't already have one
                  if (!el.getAttribute('tabindex')) {
                    el.setAttribute('tabindex', '0');
                  }
                }
              } else if (el.closest('.react-select__dropdown-indicator') || el.closest('.react-select__clear-indicator')) {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'button');
                  // Only add tabindex if element doesn't already have one
                  if (!el.getAttribute('tabindex')) {
                    el.setAttribute('tabindex', '0');
                  }
                }
              }
            });

            // Fix HTML5 landmark elements that need proper ARIA attributes
            document.querySelectorAll('main, footer, section, div[aria-label*="content"], div[class*="flex"][class*="flex-col"], div[class*="max-md:w-full"], div[class*="bg-on-background"], div[class*="items-center"], div[class*="mb-4"][class*="mt-4"]').forEach(el => {
              // Fix main elements
              if (el.tagName.toLowerCase() === 'main') {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'main');
                }
                if (!el.getAttribute('aria-label')) {
                  el.setAttribute('aria-label', 'Main content');
                }
              }

              // Fix footer elements
              else if (el.tagName.toLowerCase() === 'footer') {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'contentinfo');
                }
                if (!el.getAttribute('aria-label')) {
                  el.setAttribute('aria-label', 'Footer');
                }
              }

              // Fix section elements that should be main content
              else if (el.tagName.toLowerCase() === 'section' &&
                       el.classList.toString().includes('w-full') &&
                       el.classList.toString().includes('flex-col')) {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'main');
                }
                if (!el.getAttribute('aria-label')) {
                  el.setAttribute('aria-label', 'Main content area');
                }
              }

              // Fix div elements that should be main content areas
              else if (el.tagName.toLowerCase() === 'div' &&
                       (el.getAttribute('aria-label')?.includes('content') ||
                        (el.classList.toString().includes('flex') &&
                         el.classList.toString().includes('flex-col') &&
                         el.classList.toString().includes('justify-center')))) {
                if (!el.getAttribute('role')) {
                  el.setAttribute('role', 'main');
                }
                if (!el.getAttribute('aria-label')) {
                  el.setAttribute('aria-label', 'Application form content');
                }
              }
            });

            // Fix additional elements that act as buttons but use non-button tags
            document.querySelectorAll('span[aria-live="polite"], div[class*="react-select__indicator"], span[id*="react-select-"][class*="css-7pg0cj-a11yText"], span[class*="css-"][class*="a11yText"], div.react-select__indicators').forEach(el => {
              // Skip if already has role="button" or is inside a proper button
              if (el.getAttribute('role') === 'button' || el.closest('button')) return;

              // Check if element acts as a button (has click handlers, tabindex, or is interactive)
              const hasClickHandler = el.onclick || el.getAttribute('onclick');
              const hasTabindex = el.getAttribute('tabindex');
              const isInteractive = el.classList.contains('react-select__dropdown-indicator') ||
                                   el.classList.contains('react-select__clear-indicator') ||
                                   el.classList.contains('react-select__multi-value__remove');

              // Special case for aria-live elements with a11yText class that need role="button"
              const isA11yTextElement = el.classList.toString().includes('a11yText') ||
                                       el.classList.toString().includes('A11yText');
              const isAriaLiveElement = el.getAttribute('aria-live') === 'polite';

              // Special case for react-select indicators container
              const isIndicatorsContainer = el.classList.contains('react-select__indicators');

              if (hasClickHandler || hasTabindex || isInteractive || isIndicatorsContainer || (isAriaLiveElement && isA11yTextElement)) {
                // Remove existing role if it's "log" and replace with "button"
                if (el.getAttribute('role') === 'log') {
                  el.removeAttribute('role');
                }

                el.setAttribute('role', 'button');
                // Only add tabindex if element doesn't already have one
                // This prevents interfering with skip links which have tabindex="1"
                if (!el.getAttribute('tabindex')) {
                  el.setAttribute('tabindex', '0');
                }
                if (!el.getAttribute('aria-label')) {
                  if (el.classList.contains('react-select__dropdown-indicator')) {
                    el.setAttribute('aria-label', 'Open dropdown menu');
                  } else if (el.classList.contains('react-select__clear-indicator')) {
                    el.setAttribute('aria-label', 'Clear selection');
                  } else if (el.classList.contains('react-select__multi-value__remove')) {
                    const valueText = el.closest('.react-select__multi-value')?.querySelector('.react-select__multi-value__label')?.textContent || 'item';
                    el.setAttribute('aria-label', 'Remove ' + valueText);
                  } else if (isA11yTextElement) {
                    el.setAttribute('aria-label', 'Screen reader text');
                  } else if (isIndicatorsContainer) {
                    el.setAttribute('aria-label', 'Dropdown controls');
                  } else {
                    el.setAttribute('aria-label', 'Interactive element');
                  }
                }
              }
            });
          }

          // Run fixes immediately and on DOM changes
          addReactSelectRoles();

          // Set up mutation observer
          const observer = new MutationObserver(() => {
            setTimeout(addReactSelectRoles, 0);
          });
          observer.observe(document.body, { childList: true, subtree: true });

          // Also run on clicks to catch menu state changes
          document.addEventListener('click', () => setTimeout(addReactSelectRoles, 100));
        `}
      </Script>
      {/* Osano Style */}
      <style jsx global>{`
        .osano-cm-button--type_denyAll {
          display: none !important;
        }
        .osano-cm-widget:focus,
        .osano-cm-widget:hover {
          outline: none !important;
        }
      `}</style>
      {/* Google Consent Script */}
      <Script id="google-consent" strategy="beforeInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('consent','default',{
            'ad_storage':'denied',
            'analytics_storage':'denied',
            'ad_user_data':'denied',
            'ad_personalization':'denied',
            'wait_for_update': 500
          });
          gtag("set", "ads_data_redaction", true);
        `}
      </Script>
      {/* Osano Script */}
      {osanoId && (
        <Script
          src={`https://cmp.osano.com/${osanoId}/osano.js`}
          strategy="beforeInteractive"
          async
          crossOrigin="anonymous"
          integrity={async () => {
            (await generateHashFromUrl(
              `https://cmp.osano.com/${osanoId}/osano.js`
            )) || undefined;
          }}
        />
      )}
      {/* Google Tag Manager */}
      {gtmConfig ? (
        <Script id="google-tag-manager" strategy="beforeInteractive">
          {isProd
            ? // Production GTM script
              `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmConfig.id}');
          `
            : // Development GTM script
              `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl+ '&gtm_auth=${gtmConfig.auth}&gtm_preview=${gtmConfig.preview}&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmConfig.id}');
          `}
        </Script>
      ) : null}
    </>
  );
}
