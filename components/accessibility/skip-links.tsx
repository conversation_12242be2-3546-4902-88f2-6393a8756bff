"use client";

import React, { useEffect } from "react";

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

const defaultLinks: SkipLink[] = [
  { href: "#maincontent", label: "Skip to main content" },
];

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = defaultLinks,
  className = "",
}) => {
  // Ensure skip links are properly positioned in tab order
  useEffect(() => {
    let hasTabbed = false;

    // Add a small delay to ensure all other elements are rendered
    const timer = setTimeout(() => {
      const skipLinks = document.querySelectorAll(".skip-links");
      skipLinks.forEach((link) => {
        // Ensure skip links maintain their tabindex priority
        if (
          !link.getAttribute("tabindex") ||
          link.getAttribute("tabindex") !== "1"
        ) {
          link.setAttribute("tabindex", "1");
        }
      });
    }, 100);

    // Handle first tab press to ensure skip links get focus
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Tab" && !hasTabbed && !event.shiftKey) {
        hasTabbed = true;
        const firstSkipLink = document.querySelector(
          ".skip-links"
        ) as HTMLElement;
        if (firstSkipLink && document.activeElement !== firstSkipLink) {
          event.preventDefault();
          firstSkipLink.focus();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      clearTimeout(timer);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);
  const handleSkipLinkClick = (
    event:
      | React.MouseEvent<HTMLAnchorElement>
      | React.KeyboardEvent<HTMLAnchorElement>
  ) => {
    const href = event.currentTarget.getAttribute("href");
    if (!href) return;

    event.preventDefault();

    // Find the target element
    const targetElement = document.querySelector(href);
    if (targetElement) {
      // Ensure the element is focusable
      const originalTabIndex = targetElement.getAttribute("tabindex");
      const needsTabIndex = originalTabIndex === null;

      if (needsTabIndex) {
        targetElement.setAttribute("tabindex", "-1");
      }

      // Scroll to the element first
      targetElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });

      // Focus the element after a short delay to ensure scrolling completes
      setTimeout(() => {
        (targetElement as HTMLElement).focus();
      }, 100);

      // Remove temporary tabindex after focus is set
      if (needsTabIndex) {
        setTimeout(() => {
          targetElement.removeAttribute("tabindex");
        }, 200);
      }
    }
  };

  return (
    <nav aria-label="Skip navigation links" className={className}>
      {links.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className="skip-links"
          // Use tabIndex={1} to ensure skip links are first in tab order
          // This is necessary because accessibility fixes add tabindex="0" to many elements
          // and there may be naturally focusable elements (buttons, links) in the DOM
          tabIndex={0}
          onClick={handleSkipLinkClick}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSkipLinkClick(e as any);
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </nav>
  );
};

export default SkipLinks;
