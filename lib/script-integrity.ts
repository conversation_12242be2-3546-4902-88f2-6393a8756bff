/**
 * Script Integrity Management
 *
 * This module provides utilities for managing Subresource Integrity (SRI) hashes
 * for external scripts. SRI helps ensure that scripts haven't been tampered with.
 *
 * Usage:
 * 1. For static scripts with known hashes, use the SCRIPT_INTEGRITY_HASHES object
 * 2. For dynamic scripts (like GTM, Osano), SRI is not practical as they change frequently
 * 3. Use generateScriptIntegrityHash() to generate hashes for new scripts
 * 4. Use generateHashFromUrl() to fetch and hash scripts from URLs
 * 5. Use hashUrlAndAdd() to automatically add hashes to the runtime registry
 * 6. Use batchGenerateHashes() to process multiple URLs at once
 */

// Known integrity hashes for static external scripts
// These should be updated when the scripts change
export const SCRIPT_INTEGRITY_HASHES = {
  // Example static scripts - update these with actual hashes when available
  // 'https://example.com/static-script.js': 'sha256-ABC123...',
  // Example: Bootstrap JS (if used)
  // 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js': 'sha256-HwpjHUQdl7+5Ub2w+qMvKqGGdQhJqJJJvKqGGdQhJqJ=',
  // Example: jQuery (if used)
  // 'https://code.jquery.com/jquery-3.6.0.min.js': 'sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=',
  // Note: Dynamic scripts like GTM, Osano, and reCAPTCHA change frequently
  // and cannot use SRI without regular hash updates
} as const;

/**
 * Get the integrity hash for a script URL
 * @param url - The script URL
 * @returns The integrity hash or undefined if not available
 */
export function getScriptIntegrity(url: string): string | undefined {
  return SCRIPT_INTEGRITY_HASHES[url as keyof typeof SCRIPT_INTEGRITY_HASHES];
}

/**
 * Generate SHA256 hash for a script (for development/testing purposes)
 * Note: In production, you should get hashes from the script provider or generate them
 * using proper tools like openssl or online SRI generators
 *
 * @param scriptContent - The script content to hash
 * @returns Promise<string> - The SHA256 hash in the format "sha256-HASH"
 */
export async function generateScriptIntegrityHash(
  scriptContent: string
): Promise<string> {
  if (typeof window === "undefined") {
    // Server-side: Use Node.js crypto (if available)
    try {
      const crypto = require("crypto");
      const hash = crypto
        .createHash("sha256")
        .update(scriptContent, "utf8")
        .digest("base64");
      return `sha256-${hash}`;
    } catch (error) {
      console.warn("Node.js crypto not available for hash generation");
      return "";
    }
  } else {
    // Client-side: Use Web Crypto API
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(scriptContent);
      const hashBuffer = await crypto.subtle.digest("SHA-256", data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray));
      return `sha256-${hashBase64}`;
    } catch (error) {
      console.warn("Web Crypto API not available for hash generation");
      return "";
    }
  }
}

/**
 * Fetch script content from URL and generate its SHA256 integrity hash
 * @param url - The script URL to fetch and hash
 * @returns Promise<string> - The SHA256 hash in the format "sha256-HASH"
 */
export async function generateHashFromUrl(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(
        `Failed to fetch script: ${response.status} ${response.statusText}`
      );
    }

    const scriptContent = await response.text();
    return await generateScriptIntegrityHash(scriptContent);
  } catch (error) {
    console.error(`Error generating hash for URL ${url}:`, error);
    return "";
  }
}

/**
 * Generate hash for a URL and automatically add it to the SCRIPT_INTEGRITY_HASHES
 * This is a utility function for development - in production, hashes should be pre-computed
 * @param url - The script URL to hash and add
 * @returns Promise<string> - The generated hash
 */
export async function hashUrlAndAdd(url: string): Promise<string> {
  try {
    const hash = await generateHashFromUrl(url);
    if (hash) {
      // Note: This modifies the runtime object but won't persist across restarts
      // For persistent storage, you'd need to update the source file or use a database
      (SCRIPT_INTEGRITY_HASHES as any)[url] = hash;
      console.log(`Generated and added hash for ${url}: ${hash}`);
      return hash;
    }
    return "";
  } catch (error) {
    console.error(`Error in hashUrlAndAdd for ${url}:`, error);
    return "";
  }
}

/**
 * Create a script element with integrity if available
 * @param src - Script source URL
 * @param options - Additional script options
 * @returns HTMLScriptElement with integrity set if available
 */
export function createScriptWithIntegrity(
  src: string,
  options: {
    async?: boolean;
    defer?: boolean;
    crossOrigin?: string;
    integrity?: string;
  } = {}
): HTMLScriptElement {
  const script = document.createElement("script");
  script.src = src;

  if (options.async) script.async = true;
  if (options.defer) script.defer = true;
  if (options.crossOrigin) script.crossOrigin = options.crossOrigin;

  // Use provided integrity or look up from known hashes
  const integrity = options.integrity || getScriptIntegrity(src);
  if (integrity) {
    script.integrity = integrity;
  }

  return script;
}

/**
 * Batch generate hashes for multiple URLs
 * @param urls - Array of script URLs to hash
 * @returns Promise<Record<string, string>> - Object mapping URLs to their hashes
 */
export async function batchGenerateHashes(
  urls: string[]
): Promise<Record<string, string>> {
  const results: Record<string, string> = {};

  for (const url of urls) {
    try {
      const hash = await generateHashFromUrl(url);
      if (hash) {
        results[url] = hash;
      }
    } catch (error) {
      console.error(`Failed to generate hash for ${url}:`, error);
    }
  }

  return results;
}

/**
 * Generate TypeScript code for updating SCRIPT_INTEGRITY_HASHES
 * This is useful for development when you want to add multiple hashes at once
 * @param hashes - Object mapping URLs to their hashes
 * @returns string - TypeScript code to replace the SCRIPT_INTEGRITY_HASHES object
 */
export function generateHashesCode(hashes: Record<string, string>): string {
  const entries = Object.entries(hashes)
    .map(([url, hash]) => `  '${url}': '${hash}',`)
    .join("\n");

  return `export const SCRIPT_INTEGRITY_HASHES = {
${entries}
} as const;`;
}

/**
 * Instructions for generating SRI hashes for external scripts:
 *
 * 1. Using OpenSSL command line:
 *    curl -s https://example.com/script.js | openssl dgst -sha256 -binary | openssl base64 -A
 *
 * 2. Using online SRI generators:
 *    - https://www.srihash.org/
 *    - https://report-uri.com/home/<USER>
 *
 * 3. For scripts that change frequently (GTM, Osano, reCAPTCHA):
 *    - Consider using CSP nonce instead of SRI
 *    - Or implement a system to regularly update hashes
 *    - Or accept the security trade-off and use crossorigin="anonymous" only
 *
 * 4. Best practices:
 *    - Always use crossorigin="anonymous" with integrity
 *    - Monitor for script changes that would break SRI
 *    - Have a fallback strategy if SRI fails
 *    - Consider using CSP script-src with nonces for dynamic scripts
 */
