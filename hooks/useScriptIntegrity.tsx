import { useCallback, useEffect, useState } from 'react';
import { 
  generateHashFromUrl, 
  hashUrlAndAdd, 
  getScriptIntegrity,
  createScriptWithIntegrity 
} from '@/lib/script-integrity';

interface UseScriptIntegrityReturn {
  generateHash: (url: string) => Promise<string>;
  addHashForUrl: (url: string) => Promise<string>;
  getHash: (url: string) => string | undefined;
  createScript: (url: string, options?: {
    async?: boolean;
    defer?: boolean;
    crossOrigin?: string;
    integrity?: string;
  }) => HTMLScriptElement;
  isGenerating: boolean;
  error: string | null;
}

/**
 * React hook for managing script integrity hashes
 * Provides utilities to generate, store, and apply SRI hashes to scripts
 */
export const useScriptIntegrity = (): UseScriptIntegrityReturn => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateHash = useCallback(async (url: string): Promise<string> => {
    setIsGenerating(true);
    setError(null);
    
    try {
      const hash = await generateHashFromUrl(url);
      if (!hash) {
        throw new Error('Failed to generate hash');
      }
      return hash;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return '';
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const addHashForUrl = useCallback(async (url: string): Promise<string> => {
    setIsGenerating(true);
    setError(null);
    
    try {
      const hash = await hashUrlAndAdd(url);
      if (!hash) {
        throw new Error('Failed to generate and add hash');
      }
      return hash;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return '';
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const getHash = useCallback((url: string): string | undefined => {
    return getScriptIntegrity(url);
  }, []);

  const createScript = useCallback((
    url: string, 
    options: {
      async?: boolean;
      defer?: boolean;
      crossOrigin?: string;
      integrity?: string;
    } = {}
  ): HTMLScriptElement => {
    return createScriptWithIntegrity(url, options);
  }, []);

  return {
    generateHash,
    addHashForUrl,
    getHash,
    createScript,
    isGenerating,
    error
  };
};

/**
 * Example usage:
 * 
 * ```tsx
 * function MyComponent() {
 *   const { generateHash, addHashForUrl, createScript, isGenerating, error } = useScriptIntegrity();
 *   
 *   const handleGenerateHash = async () => {
 *     const hash = await generateHash('https://example.com/script.js');
 *     console.log('Generated hash:', hash);
 *   };
 *   
 *   const handleAddScript = async () => {
 *     const hash = await addHashForUrl('https://example.com/script.js');
 *     if (hash) {
 *       const script = createScript('https://example.com/script.js', {
 *         async: true,
 *         crossOrigin: 'anonymous'
 *       });
 *       document.head.appendChild(script);
 *     }
 *   };
 *   
 *   return (
 *     <div>
 *       <button onClick={handleGenerateHash} disabled={isGenerating}>
 *         {isGenerating ? 'Generating...' : 'Generate Hash'}
 *       </button>
 *       <button onClick={handleAddScript} disabled={isGenerating}>
 *         Add Script with Hash
 *       </button>
 *       {error && <p>Error: {error}</p>}
 *     </div>
 *   );
 * }
 * ```
 */
